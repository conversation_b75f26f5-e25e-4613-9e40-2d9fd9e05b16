// This file will inherit styles from the parent loan-search component
// The styles are already defined in the parent component's SCSS file
// We'll move the relevant styles here to keep them with the component

// CSS Custom Properties for easy text effect experimentation
// :host {
//   // Text outline style (default)
//   --text-outline-color: #fff;
//   --text-shadow-blur: 8px;
//   --text-shadow-color: rgba(0, 0, 0, 0.1);

//   // Settled letter effects
//   --settled-shadow-blur: 12px;
//   --settled-shadow-color: rgba(222, 51, 65, 0.3);
//   --settled-glow-blur: 20px;
//   --settled-glow-color: rgba(222, 51, 65, 0.1);
// }

// Alternative text effect styles (uncomment to try different effects)

// Default white outline effect
// :host {
//   --text-outline-color: #fff;
//   --text-shadow-blur: 8px;
//   --text-shadow-color: rgba(0, 0, 0, 0.1);
//   --settled-shadow-blur: 12px;
//   --settled-shadow-color: rgba(222, 51, 65, 0.3);
//   --settled-glow-blur: 20px;
//   --settled-glow-color: rgba(222, 51, 65, 0.1);
// }

// Strong outline effect
// :host {
//   --text-outline-color: #000;
//   --text-shadow-blur: 4px;
//   --text-shadow-color: rgba(255, 255, 255, 0.8);
//   --settled-shadow-blur: 8px;
//   --settled-shadow-color: rgba(222, 51, 65, 0.6);
//   --settled-glow-blur: 16px;
//   --settled-glow-color: rgba(222, 51, 65, 0.3);
// }

// Soft glow effect with black text and red glow
:host {
  --text-outline-color: transparent;
  --text-shadow-blur: 12px;
  --text-shadow-color: rgba(222, 51, 65, 0.3);
  --settled-shadow-blur: 16px;
  --settled-shadow-color: rgba(222, 51, 65, 0.5);
  --settled-glow-blur: 24px;
  --settled-glow-color: rgba(222, 51, 65, 0.2);
}

.app-title {
  text-align: center;
  margin-bottom: 3rem;
  
  h1 {
    font-size: 3.5rem;
    font-weight: 300;
    color: #000; // Black text for better contrast with red glow
    margin: 0;
    letter-spacing: 0.1em;

    .letter {
      display: inline-block;
      transition: all 0.3s ease;
      font-family: 'Courier New', monospace;

      // Text outline effect using multiple text-shadows
      text-shadow:
        -1px -1px 0 var(--text-outline-color),
        1px -1px 0 var(--text-outline-color),
        -1px 1px 0 var(--text-outline-color),
        1px 1px 0 var(--text-outline-color),
        0 0 var(--text-shadow-blur) var(--text-shadow-color);

      &.settled {
        color: #de3341; // RDS brand color
        font-weight: 400;

        // Enhanced text shadow for settled letters
        text-shadow:
          -1px -1px 0 var(--text-outline-color),
          1px -1px 0 var(--text-outline-color),
          -1px 1px 0 var(--text-outline-color),
          1px 1px 0 var(--text-outline-color),
          0 0 var(--settled-shadow-blur) var(--settled-shadow-color),
          0 0 var(--settled-glow-blur) var(--settled-glow-color);
      }
    }
  }
}
