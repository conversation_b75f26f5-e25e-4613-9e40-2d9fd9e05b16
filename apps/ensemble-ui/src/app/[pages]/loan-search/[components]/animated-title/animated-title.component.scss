// This file will inherit styles from the parent loan-search component
// The styles are already defined in the parent component's SCSS file
// We'll move the relevant styles here to keep them with the component

// CSS Custom Properties for easy text effect experimentation
// :host {
//   // Text outline style (default)
//   --text-outline-color: #fff;
//   --text-shadow-blur: 8px;
//   --text-shadow-color: rgba(0, 0, 0, 0.1);

//   // Settled letter effects
//   --settled-shadow-blur: 12px;
//   --settled-shadow-color: rgba(222, 51, 65, 0.3);
//   --settled-glow-blur: 20px;
//   --settled-glow-color: rgba(222, 51, 65, 0.1);
// }

// Alternative text effect styles (uncomment to try different effects)

// Strong outline effect
:host {
  --text-outline-color: #000;
  --text-shadow-blur: 4px;
  --text-shadow-color: rgba(255, 255, 255, 0.8);
  --settled-shadow-blur: 8px;
  --settled-shadow-color: rgba(222, 51, 65, 0.6);
  --settled-glow-blur: 16px;
  --settled-glow-color: rgba(222, 51, 65, 0.3);
}

// Soft glow effect
// :host {
//   --text-outline-color: transparent;
//   --text-shadow-blur: 12px;
//   --text-shadow-color: rgba(0, 0, 0, 0.3);
//   --settled-shadow-blur: 16px;
//   --settled-shadow-color: rgba(222, 51, 65, 0.5);
//   --settled-glow-blur: 24px;
//   --settled-glow-color: rgba(222, 51, 65, 0.2);
// }

.app-title {
  text-align: center;
  margin-bottom: 3rem;
  
  h1 {
    font-size: 3.5rem;
    font-weight: 300;
    color: #333;
    margin: 0;
    letter-spacing: 0.1em;

    .letter {
      display: inline-block;
      transition: all 0.3s ease;
      font-family: 'Courier New', monospace;

      // Text outline effect using multiple text-shadows
      text-shadow:
        -1px -1px 0 #fff,
        1px -1px 0 #fff,
        -1px 1px 0 #fff,
        1px 1px 0 #fff,
        0 0 8px rgba(0, 0, 0, 0.1);

      &.settled {
        color: #de3341; // RDS brand color
        font-weight: 400;

        // Enhanced text shadow for settled letters
        text-shadow:
          -1px -1px 0 #fff,
          1px -1px 0 #fff,
          -1px 1px 0 #fff,
          1px 1px 0 #fff,
          0 0 12px rgba(222, 51, 65, 0.3),
          0 0 20px rgba(222, 51, 65, 0.1);
      }
    }
  }
}
